import { Box, Drawer, I<PERSON><PERSON>utton, Stack, Typography } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { disableNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/disable-native-drag-preview';
import { preventUnhandled } from '@atlaskit/pragmatic-drag-and-drop/prevent-unhandled';
import type { DragLocationHistory } from '@atlaskit/pragmatic-drag-and-drop/types';
import { SlidersHorizontalOutlinedIcon } from '@ui/components/StandardIcons/SlidersHorizontalOutlinedIcon';
import { SidebarOutlinedIcon } from '@ui/components/StandardIcons/SidebarOutlinedIcon';
import { MenuOpenOutlinedIcon } from '@ui/components/StandardIcons/MenuOpenOutlinedIcon';
import { WarningIcon } from '../WarningIcon';
import { DragHandleIcon } from '@ui/components/StandardIcons/DragHandleIcon';
import { beige, brandYellow } from '@ui/theme/colors';

export type SidebarProps = {
  hasWarnings?: boolean;
  defaultToOpenOnWarnings?: boolean;
  title: string;
  titleIcon?: React.ReactNode;
  description?: string;
  children?: React.ReactNode;
};

type DragState =
  | {
      type: 'idle';
    }
  | {
      type: 'dragging';
    };

/**
 * Calculate the proposed width based on the drag location and constraints
 */
function getProposedWidth({ initialWidth, location }: { initialWidth: number; location: DragLocationHistory }): number {
  const diffX = location.initial.input.clientX - location.current.input.clientX;
  const proposedWidth = initialWidth + diffX;
  const maxWidth = window.innerWidth / 2;
  return Math.min(Math.max(MIN_WIDTH, proposedWidth), maxWidth);
}

const MIN_WIDTH = 250;
const INITIAL_WIDTH = 350;

export function Sidebar({
  hasWarnings = false,
  defaultToOpenOnWarnings = true,
  title,
  titleIcon = <SlidersHorizontalOutlinedIcon />,
  description,
  children,
}: Readonly<SidebarProps>) {
  const [sidebarOpen, setSidebarOpen] = useState(hasWarnings && defaultToOpenOnWarnings);
  const [initialWidth, setInitialWidth] = useState<number>(INITIAL_WIDTH);
  const [width, setWidth] = useState<number>(INITIAL_WIDTH);
  const [dragState, setDragState] = useState<DragState>({ type: 'idle' });
  const dragHandleRef = useRef<HTMLDivElement | null>(null);
  const contentContainerRef = useRef<HTMLDivElement | null>(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const topSentinelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const sentinel = topSentinelRef.current;
    const container = contentContainerRef.current;
    if (!sentinel || !container) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        // if the sentinel is NOT visible, we've scrolled down
        console.log(entry);
        setIsScrolled(!entry.isIntersecting);
      },
      {
        root: container, // the scrollable area
        threshold: 1.0,
      },
    );

    observer.observe(sentinel);
    return () => observer.disconnect();
  }, [sidebarOpen]);

  /**
   * Due to some kind of black magic, the magicplan iframe swallows mousemove events from the pragmatic
   * drag and drop library. Therefore, when we start dragging, we need to disable pointer events on the iframe,
   * and re-enable them when we stop dragging. We also call this on component unmount just in case that happens
   * while we are dragging
   * @param pointerEvents
   */
  const setIFramePointerEvents = (pointerEvents: string) => {
    const iFrames = document.querySelectorAll('iframe');
    iFrames.forEach((iframe) => {
      iframe.style.pointerEvents = pointerEvents;
    });
  };

  // Clean up iframe pointer events on unmount
  useEffect(() => {
    return () => {
      setIFramePointerEvents('all');
    };
  }, []);

  useEffect(() => {
    const handle = dragHandleRef.current;
    if (!handle) return;

    return draggable({
      element: handle,
      onGenerateDragPreview: ({ nativeSetDragImage }) => {
        // Disable native preview & animations for smooth UX
        disableNativeDragPreview({ nativeSetDragImage });
        preventUnhandled.start();
      },
      onDragStart() {
        setDragState({ type: 'dragging' });
        setIFramePointerEvents('none');
      },
      onDrag({ location }) {
        const proposedWidth = getProposedWidth({
          initialWidth,
          location,
        });
        setWidth(proposedWidth);
      },
      onDrop({ location }) {
        preventUnhandled.stop();
        setDragState({ type: 'idle' });
        const finalWidth = getProposedWidth({
          initialWidth,
          location,
        });
        setInitialWidth(finalWidth);
        setIFramePointerEvents('all');
      },
    });
  }, [initialWidth]);

  const headerBoxShadow = isScrolled ? '0px 8px 17px #00000020' : 'none';
  return (
    <>
      <IconButton
        onClick={() => setSidebarOpen(true)}
        sx={{
          position: 'fixed',
          right: !sidebarOpen ? 0 : width,
          opacity: sidebarOpen ? 0 : 1,
          top: '50%',
          zIndex: 1200,
          backgroundColor: hasWarnings ? 'warning.light' : 'background.paper',
          border: hasWarnings ? '2px solid' : '1px solid',
          borderRadius: '8px 0 0 8px',
          borderColor: 'divider',
          boxShadow: 2,
          '&:hover': {
            backgroundColor: hasWarnings ? brandYellow[300] : 'action.hover',
          },
          transition: 'right 225ms cubic-bezier(0, 0, 0.2, 1) 0ms, opacity 250ms',
          ...(hasWarnings && {
            borderColor: 'warning.main',
          }),
          ...(sidebarOpen && { pointerEvents: 'none' }),
        }}
      >
        <Stack direction="column" alignItems="center" gap={1}>
          {hasWarnings && (
            <WarningIcon x={2.5} y={2.5} iconWidth={20} iconHeight={20} canvasWidth={20} canvasHeight={20} />
          )}
          <SidebarOutlinedIcon />
        </Stack>
      </IconButton>
      <Drawer
        variant="persistent"
        anchor="right"
        open={sidebarOpen}
        sx={{
          height: '100%',
          zIndex: 1100,
          boxSizing: 'border-box',
          padding: 0,
        }}
        slotProps={{
          paper: {
            sx: {
              width: `${width}px`,
              height: '100%',
              display: 'flex',
              flexDirection: 'row',
              boxShadow: 3,
              backgroundColor: beige[100],
              padding: 0,
            },
          },
        }}
      >
        <Box
          ref={dragHandleRef}
          role="separator"
          aria-orientation="vertical"
          aria-label="Resize sidebar"
          sx={{
            position: 'absolute',
            height: '100%',
            width: '8px',
            left: 0,
            top: 0,
            cursor: 'ew-resize',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderColor: 'divider',
            userSelect: 'none',
            flexShrink: 0,
          }}
        >
          <DragHandleIcon width={4} height={16} />
        </Box>
        <Stack sx={{ width: '100%' }}>
          <Stack
            sx={{
              position: 'sticky',
              top: 0,
              zIndex: 2,
              backgroundColor: beige[100],
              padding: 1,
              transition: 'box-shadow 0.2s',
              boxShadow: headerBoxShadow,
            }}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <Stack direction="row" alignItems="center" gap={1}>
              {titleIcon}
              <Typography variant="headline4">{title}</Typography>
            </Stack>
            <IconButton sx={{ padding: '4px' }} aria-label="Close sidebar" onClick={() => setSidebarOpen(false)}>
              <MenuOpenOutlinedIcon viewBox="0 0 20 24" />
            </IconButton>
          </Stack>

          <Stack direction="row" sx={{ flex: 1, overflow: 'hidden' }}>
            <Stack
              ref={contentContainerRef}
              direction="column"
              gap={2}
              padding="16px 0"
              sx={{
                flex: 1,
                paddingLeft: '8px',
                minWidth: 0,
                overflow: 'auto',
                ...(dragState.type === 'dragging' && { pointerEvents: 'none' }),
              }}
            >
              <div ref={topSentinelRef} style={{ height: '1px', width: '100%' }} />

              {description}
              {children}
            </Stack>
          </Stack>
        </Stack>
      </Drawer>
    </>
  );
}
