import { lookupUValueForSurfaceType, projectUValuesDefaultsAreValid } from './utils/helpers';
import { HousePersonOutsideOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutsideOutlinedIcon';
import { HeatDesignSidebarCategory, HeatDesignSidebarModules } from './components/sidebar/HeatDesignSidebarModules';
import { propertyIsValid } from './Validator';
import { OtherHousesIcon } from '@ui/components/Icons/OtherHouses/OtherHousesIcon';
import { HouseUValues } from './components/sidebar/HouseUValues';
import { WindOutlinedIcon } from '@ui/components/StandardIcons/WindOutlinedIcon';
import { VentilationForm } from './dwelling/VentilationForm';
import { FloorPlanIcon } from '@ui/components/Icons/FloorPlan/FloorPlanIcon';
import { GrassIcon } from '@ui/components/Icons/Grass/GrassIcon';
import { SoilLevel } from './components/sidebar/SoilLevel';
import { FloorUValues } from './components/sidebar/FloorUValues';
import { useHeatDesignHouseInputsStore } from './stores/HouseInputsStore';
import { useUValuesStore } from './stores/UValuesStore';
import { useFloors } from './stores/FloorsStore';
import { useRooms } from './stores/RoomsStore';
import { useHeatDesignUI } from './stores/HeatDesignUIStore';
import { ROOM_FABRIC_TYPES } from './stores/types';
import { useMemo } from 'react';
import { isNotNullish } from '../../utils/isNotNullish';

export function HeatDesignSidebarContent() {
  const rooms = useRooms();
  const floors = useFloors();
  const { projectUValues } = useUValuesStore();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floor = floors.find((f) => f.uid === selectedFloorId);
  const errors = useMemo(() => {
    return {
      uValues: !projectUValuesDefaultsAreValid(projectUValues, rooms, floors),
      acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
      pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
      soilPercentage: !isNotNullish(floor?.soilPercentageDefault),
      floorUValues: !ROOM_FABRIC_TYPES.every((fabricType) => {
        if (!floor) {
          return false;
        }
        return !!lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors)?.uValue;
      }),
    };
  }, [floor, floors, projectUValues, rooms, ventilationDesign]);
  const hasHouseDefaultWarning = errors.uValues || errors.acphDefault || errors.pulseTest;
  const hasHouseVentilationWarning = errors.acphDefault || errors.pulseTest;
  const hasFloorDefaultsWarning = errors.soilPercentage || errors.floorUValues;
  const categories: HeatDesignSidebarCategory[] = [
    {
      id: 'house-defaults',
      title: 'House Defaults',
      icon: <HousePersonOutsideOutlinedIcon />,
      hasWarning: hasHouseDefaultWarning,
      isDefaultExpanded: hasHouseDefaultWarning,
      subcategories: [
        {
          id: 'house-u-values',
          title: 'U-values',
          icon: <OtherHousesIcon />,
          component: <HouseUValues />,
          hasWarning: errors.uValues,
          isDefaultExpanded: errors.uValues,
        },
        {
          id: 'ventilation',
          title: 'Ventilation',
          icon: <WindOutlinedIcon />,
          component: <VentilationForm />,
          hasWarning: hasHouseVentilationWarning,
          isDefaultExpanded: hasHouseVentilationWarning,
        },
      ],
    },
    {
      id: 'floor-defaults',
      title: 'Floor defaults',
      icon: <FloorPlanIcon />,
      hasWarning: hasFloorDefaultsWarning,
      isDefaultExpanded: hasFloorDefaultsWarning,
      subcategories: [
        {
          id: 'soil-level',
          title: 'Soil level',
          icon: <GrassIcon />,
          component: <SoilLevel />,
          hasWarning: errors.soilPercentage,
          isDefaultExpanded: errors.soilPercentage,
        },
        {
          id: 'floor-u-values',
          title: 'U-values',
          icon: <OtherHousesIcon />,
          component: <FloorUValues />,
          hasWarning: errors.floorUValues,
          isDefaultExpanded: errors.floorUValues,
        },
      ],
    },
  ];
  return <HeatDesignSidebarModules categories={categories} />;
}
