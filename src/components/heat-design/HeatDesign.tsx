import { Accordion } from '@ui/components/Accordion/Accordion';
import { DeviceOutlinedIcon } from '@ui/components/StandardIcons/DeviceOutlinedIcon';
import { ProgressBarStepper } from '@ui/components/ProgressBarStepper/ProgressBarStepper';
import { Box, Stack, useMediaQuery } from '@mui/material';
import React, { useEffect } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { useGroundwork } from 'context/groundwork-context';
import { SolutionLoader } from 'components/quotation/sections/SolutionLoader';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { api } from '../../utils/api';
import { getUrlQueryParameter } from './utils/helpers';
import { heatDesignTheme } from './HeatDesignTheme';
import PageNavigation from './navigation/PageNavigation';
import HeatDesignLoader from './components/HeatDesignLoader';
import { ClimateDataLoader } from './components/climate/ClimateDataLoader';
import { HlcPage, usePageNavigationStore } from './stores/PageNavigationStore';
import Modals from './modals/Modals';
import PageComponent from './PageComponent';
import { ErrorBoundary } from './components/errors/ErrorBoundary';
import { ErrorLayout } from './components/ErrorLayout';
import { HeatDesignErrorInfo } from './components/errors/HeatDesignErrorInfo';
import { useHeatDesignUI } from './stores/HeatDesignUIStore';
import { useProtobufHeatDesignStore } from './stores/ProtobufHeatDesignStore';
import { TechnicalSurveySidebar } from './TechnicalSurveySidebar';
import { useIsSidebarEnabled } from './hooks/useSidebarToggle';
import { Sidebar } from './components/sidebar/Sidebar';
import { HeatDesignSidebarContent } from './HeatDesignSidebarContent';

export default function HeatDesign({ includeReload = false }: { includeReload?: boolean }) {
  const { groundwork } = useGroundwork();
  const isDemoData = getUrlQueryParameter({ key: 'demo-data' });
  const enableTechnicalSurveySidebar = getUrlQueryParameter({ key: 'technical-survey' });
  const isSidebarEnabled = useIsSidebarEnabled();
  const { id: groundworkId } = groundwork;
  const setInitialPageNavigationData = usePageNavigationStore((s) => s.actions.setInitialData);
  const currentIndex = usePageNavigationStore((s) => s.currentIndex);
  const isLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const page = usePageNavigationStore((s) => s.page);

  const { formatMessage } = useIntl();
  const energySolutionId = useEnergySolutionId();
  const setIsOperationInProgress = useHeatDesignUI((s) => s.actions.setIsOperationInProgress);

  const { refetch: refetchHeatDesign, isLoading: isLoadingHeatDesign } = api.HeatLossCalculator.loadHeatDesign.useQuery(
    { energySolutionId: energySolutionId!, installationGroundworkId: groundworkId!.value!, demoData: isDemoData },
    {
      enabled: !!energySolutionId,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
    },
  );

  useEffect(() => {
    setIsOperationInProgress(isLoadingHeatDesign);
  }, [isLoadingHeatDesign, setIsOperationInProgress]);

  useEffect(() => {
    const pages = [
      HlcPage.PROPERTY_DETAILS,
      HlcPage.FLOOR_OVERVIEW,
      HlcPage.HEAT_LOSS_OVERVIEW,
      HlcPage.RADIATORS_OVERVIEW,
      HlcPage.PRODUCT_SELECTION,
      HlcPage.REPORT,
    ];

    const localizedPages = pages.map((p) => formatMessage({ id: p }));

    const page = pages[0]!;

    setInitialPageNavigationData({ page, pages, localizedPages, currentIndex: 0, previouslyVisitedPage: undefined });
  }, [formatMessage, includeReload, refetchHeatDesign, setInitialPageNavigationData]);

  const isMobile = useMediaQuery('(max-width: 700px)');
  const localizedPages = usePageNavigationStore((s) => s.localizedPages);
  const sidebarEnabledPages = [HlcPage.FLOOR_OVERVIEW, HlcPage.HEAT_LOSS_OVERVIEW];

  return (
    <AiraThemeProvider theme={heatDesignTheme}>
      <ErrorBoundary
        renderFallback={(message: string) => (
          <ErrorLayout
            icon={DeviceOutlinedIcon}
            heading={formatMessage({ id: 'heatDesign.error.application.title' })}
            details={
              <>
                <HeatDesignErrorInfo
                  description={formatMessage({ id: 'heatDesign.error.application.title' })}
                  solutionId={energySolutionId}
                  groundworkId={groundworkId?.value}
                />
                <br />
                <Accordion
                  defaultExpanded={false}
                  sx={{ paddingTop: 1, paddingBottom: 0, overflowWrap: 'break-word' }}
                  header="Console log"
                  headingVariant="body1"
                >
                  {message}
                </Accordion>
              </>
            }
          >
            <FormattedMessage id="heatDesign.error.application.description" />
          </ErrorLayout>
        )}
      >
        <HeatDesignLoader>
          <ClimateDataLoader>
            <SolutionLoader>
              <Box
                data-testid="heat-design-container"
                component="section"
                sx={{
                  overflowX: 'visible',
                  overflowY: 'auto',
                  height: '100%',
                  padding: '32px 32px 40px 32px',
                  maxWidth: isMobile ? '100vw' : '100%',
                  display: 'flex',
                  flex: '1 1 auto',
                  flexDirection: 'column',
                  // Remove the height shenanigans when printing the page so that the
                  // entire document is visible.
                  '@media print': {
                    height: 'unset',
                    display: 'block',
                    padding: '0 !important',
                  },
                }}
              >
                <Stack
                  sx={{
                    width: isMobile ? '100vw' : '100%',
                    maxWidth: '100%',
                    alignSelf: 'center',
                    paddingBottom: '90px',
                    '@media print': {
                      display: 'block',
                      paddingBottom: 0,
                    },
                  }}
                  spacing={3}
                  marginBottom={isLocked ? '150px' : 0}
                >
                  {localizedPages.length > 1 && (
                    <Box sx={{ width: '100%', maxWidth: 880, marginX: 'auto !important', displayPrint: 'none' }}>
                      <ProgressBarStepper pages={localizedPages} activeIndex={currentIndex} />
                    </Box>
                  )}
                  <PageComponent />
                </Stack>
                <PageNavigation includeReload={includeReload} refetchHeatDesign={refetchHeatDesign} />
                <Modals />
              </Box>
              {enableTechnicalSurveySidebar && <TechnicalSurveySidebar />}
              {isSidebarEnabled && sidebarEnabledPages.includes(page) && (
                <Sidebar title={formatMessage({ id: 'heatDesign.sidebar.title' })}>
                  <HeatDesignSidebarContent />
                </Sidebar>
              )}
            </SolutionLoader>
          </ClimateDataLoader>
        </HeatDesignLoader>
      </ErrorBoundary>
    </AiraThemeProvider>
  );
}
