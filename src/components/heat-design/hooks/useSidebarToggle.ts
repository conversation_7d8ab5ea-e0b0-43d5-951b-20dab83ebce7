import { api } from 'utils/api';
import { getUrlQueryParameter } from '../utils/helpers';

export function useIsSidebarEnabled(): boolean {
  const { data: serverEnvironment } = api.AiraBackend.getServerEnvironment.useQuery();
  const isEnabled = getUrlQueryParameter({ key: 'sidebar' });

  if (isEnabled) {
    return true;
  }

  if (serverEnvironment?.environmentName === 'development') {
    return !getUrlQueryParameter({ key: 'disable-sidebar' });
  }

  return false;
}
