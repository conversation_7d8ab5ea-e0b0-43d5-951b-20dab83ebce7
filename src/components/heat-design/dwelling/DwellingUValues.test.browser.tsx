import {
  FabricType,
  HeatDesign as ProtoHeatDesign,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { fireEvent, screen } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { vi } from 'vitest';
import { IntlProvider } from 'react-intl';
import { mockGetServerEnvironment, mockGetTechnicalSpecifications_empty, mocks } from 'tests/utils/mockedTrpcCalls';
import { addCustomUValue, chooseInUValueInput, reload, renderWithProviders, trpcMsw } from 'tests/utils/testUtils';
import { CountryCode } from 'utils/marketConfigurations';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { getUValuesForCountryCode } from '../uValues';
import { createRouterMock, useRouter } from '@mocks/next/router';
import { v4 as uuidv4 } from 'uuid';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';
import { solution as asaSolution } from '../../../tests/heat-loss/fixtures/asaTestData';
import { setupWorker } from 'msw/browser';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';

useRouter.mockReturnValue({
  ...createRouterMock(),
  route: '/heat-design',
  pathname: '/heat-design',
  query: { solution: SOLUTION_ID },
  asPath: `/heat-design?solution=${SOLUTION_ID}`,
  basePath: '',
  isLocaleDomain: false,
  isFallback: false,
  isReady: true,
  isPreview: false,
  push: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  prefetch: () => Promise.resolve(),
  beforePopState: vi.fn(),
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
});

const deletedUValueID = uuidv4();
const deletedUValueName = 'Some u-value that does not exist in the country list';
const deletedUValueValue = '2.30';

const getAsaHouse = (): ProtoHeatDesign => {
  const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
  return {
    ...asaRawHouse,
    dwellingUValueDefaults: {
      defaultEntries: [
        {
          fabricType: FabricType.FABRIC_TYPE_ROOF,
          uValue: {
            id: { value: deletedUValueID },
            name: deletedUValueName,
            uValueSi: Number(deletedUValueValue),
          },
        },
      ],
    },
  };
};

let currentHeatDesign = getAsaHouse();

const server = setupWorker(
  mockGetServerEnvironment(),
  mockGetTechnicalSpecifications_empty(),
  mocks.getGrpcEnergySolution.asa,
  trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => ({
    currentSolution: asaSolution,
    lastQuotedSolution: asaSolution,
  })),
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.getHeatPumpParameters.simple,
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: currentHeatDesign,
    isLocked: false,
    result: undefined,
    events: [],
    updatedAt: new Date(),
  })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => []),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [],
  })),
  trpcMsw.InstallationGroundwork.sendHeatPumpParameters.mutation(() => ({})),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators: [] })),
  ...sharedHeatDesignHandlers,
);

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
beforeEach(async () => {
  currentHeatDesign = getAsaHouse();
  renderWithProviders(heatDesignComponent());
  await reload();
});
afterEach(() => server.resetHandlers());
afterAll(() => server.stop());

test('Ensure default u-values are selectable', async () => {
  await chooseInUValueInput('foundation', 'Ground Floor No Insulation', 1.15);

  await chooseInUValueInput(
    'externalWalls',
    'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster 13mm',
    0.45,
  );

  await addCustomUValue('internalWalls', 'Plaster, Brick 102.5mm, Plaster', 1.76);

  await addCustomUValue('partyWalls', 'Party wall 1940+', 0.5);

  await chooseInUValueInput('intermediateFloors', 'Ground Floor No Insulation', 1.15);

  await chooseInUValueInput(
    'roof',
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 300mm insulation between rafters, 9.5 mm plasterboard',
    0.12,
  );

  await chooseInUValueInput('doors', 'High Quality Door 50% glazing', 2.2);

  await chooseInUValueInput('windows', 'Standard Double Glazing Wood/PVC frame', 2.8);

  await chooseInUValueInput('roofGlazings', 'Building regulations for new-builds', 2.2);
});

test('Ensure deleted u-value is selected in dwelling defaults', async () => {
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();

  // The "deleted" u-value should be selected in the drop-down
  const input = screen.getByTestId('heat-design-uvalue-modal-input-roof');
  expect(input).toHaveValue(`${deletedUValueValue} | ${deletedUValueName}`);

  // Sanity check - this u-value should not be in the country list
  expect(
    getUValuesForCountryCode(CountryCode.GB).roof.find(
      (u) => u.value === Number(deletedUValueValue) && u.name === deletedUValueName,
    ),
  ).toBeUndefined();

  // Other values should not be set
  expect(screen.getByTestId('heat-design-uvalue-modal-input-windows')).toHaveValue('');
});

test('Ensure deleted u-value is not re-selectable if de-selected', async () => {
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();

  const existingUValueName = '100mm Insulation';
  const existingUValueValue = 0.34;
  const existingUValue = getUValuesForCountryCode(CountryCode.GB).roof.find(
    (u) => u.value === existingUValueValue && u.name === existingUValueName,
  )!;

  const input = screen.getByTestId('heat-design-uvalue-modal-input-roof');
  const select = screen.getByTestId('heat-design-uvalue-modal-select-roof');
  await userEvent.click(select);
  fireEvent.change(input, { target: { value: existingUValueName } });
  let options = await screen.findAllByRole('option');
  const existingUValueOption = options.find((o) => o.id === existingUValue.id)!;
  await userEvent.click(existingUValueOption);
  expect(input).toHaveValue(existingUValue.display());

  // Now try clicking on the disabled / deleted option
  await userEvent.click(select);
  fireEvent.change(input, { target: { value: deletedUValueName } });
  options = await screen.findAllByRole('option');
  const deletedUValueOption = options.find((o) => o.id === deletedUValueID)!;
  await expect(userEvent.click(deletedUValueOption)).rejects.toThrow(
    'Unable to perform pointer interaction as the element has `pointer-events: none`',
  );

  // Click out of the u-value input so the entered input is cleared
  const modalHeading = screen.getByRole('heading', { name: 'heatDesign.uValues.uValues' });
  await userEvent.click(modalHeading);
  expect(input).toHaveValue(existingUValue.display());
});
