import { Box, Grid, Stack, Typography } from '@mui/material';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useHeatDesignHouseInputsStore, useNumOccupants } from '../stores/HouseInputsStore';
import { propertyIsValid } from '../Validator';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { useRooms } from '../stores/RoomsStore';
import { countBathrooms, countBedrooms } from '../utils/helpers';
import { useFloors } from '../stores/FloorsStore';
import ConstructionYearForm from './ConstructionYearForm';
import { beige, grey, surface } from '@ui/theme/colors';
import dynamic from 'next/dynamic';
import { Loader } from '@googlemaps/js-api-loader';
import { useGroundwork } from '../../../context/groundwork-context';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { theme } from '@ui/theme/theme';
import { OpenInFullIcon } from '@ui/components/Icons/OpenInFullIcon/OpenInFullIcon';
import { api } from '../../../utils/api';

const PropertyDetailsMap = dynamic(() => import('./PropertyDetailsMap'), { ssr: false });

function PropertyDetails({ address, embed3dUrl }: { address: string; embed3dUrl: string }) {
  const numberOfResidents = useNumOccupants();
  const actions = useHeatDesignHouseInputsStore((s) => s.actions);
  const {
    groundwork: { location: groundworkLocation },
  } = useGroundwork();
  const { data: serverEnvironment } = api.AiraBackend.getServerEnvironment.useQuery();
  const googleApiKey = serverEnvironment?.googleApiKey;

  // Streetview state we expose to the map in order to draw the orientation marker on the map
  const [svPosition, setSvPosition] = useState<{ lat: number; lng: number } | null>(null);
  const [svHeading, setSvHeading] = useState<number | null>(null);
  const [isMapTransitionEnabled, setIsMapTransitionEnabled] = useState(false);

  const [isStreetViewFound, setIsStreetViewFound] = useState<boolean | undefined>(undefined);
  const intl = useIntl();
  const streetViewRef = useRef<HTMLDivElement | null>(null);
  const streetviewServiceRef = useRef<google.maps.StreetViewService | null>(null);
  // Refs to manage Street View instance + listeners
  const panoramaRef = useRef<google.maps.StreetViewPanorama | null>(null);
  const panoramaStatusListenerRef = useRef<google.maps.MapsEventListener | null>(null);
  const povListenerRef = useRef<google.maps.MapsEventListener | null>(null);
  const posListenerRef = useRef<google.maps.MapsEventListener | null>(null);

  const numFloors = useFloors().length;
  const rooms = useRooms();
  const numBedrooms = countBedrooms(rooms);
  const numBathrooms = countBathrooms(rooms);
  const [activeArea, setActiveArea] = useState<'map' | 'magicplan'>('magicplan');
  const [isMapExpanded, setIsMapExpanded] = useState(false);

  const location = useMemo(() => {
    const geometry =
      groundworkLocation?.$case === 'exactAddress' ? groundworkLocation.exactAddress.geometry : undefined;
    const loc = geometry
      ? {
          lat: geometry?.lat,
          lng: geometry?.long,
        }
      : undefined;
    return loc;
  }, [groundworkLocation]);

  const clearSvListeners = useCallback(() => {
    povListenerRef.current?.remove();
    posListenerRef.current?.remove();
    povListenerRef.current = null;
    posListenerRef.current = null;
  }, []);

  // Function to programmatically update the panorama location. Used on initialisation and when
  // the user moves the street view in the map
  const loadPanoramaForLatLng = useCallback(
    (latLng: google.maps.LatLng | google.maps.LatLngLiteral | null) => {
      if (!latLng || !streetViewRef.current) return;
      const { maps } = google;

      setIsStreetViewFound(undefined);

      if (!streetviewServiceRef.current) {
        streetviewServiceRef.current = new maps.StreetViewService();
      }

      const loc =
        typeof (latLng as google.maps.LatLng).lat === 'function'
          ? { lat: (latLng as google.maps.LatLng).lat(), lng: (latLng as google.maps.LatLng).lng() }
          : (latLng as google.maps.LatLngLiteral);

      streetviewServiceRef.current.getPanorama(
        { location: loc, radius: 300, sources: [google.maps.StreetViewSource.OUTDOOR] },
        (data, status) => {
          if (status !== maps.StreetViewStatus.OK || !data?.location) {
            setIsStreetViewFound(false);
            return;
          }

          setIsStreetViewFound(true);

          const panoId = data.location.pano;
          const panoLatLng = data.location.latLng ?? new maps.LatLng(loc.lat, loc.lng);

          // Create or update the panorama strictly by pano ID
          const options: google.maps.StreetViewPanoramaOptions = {
            pano: panoId,
            addressControl: false,
            linksControl: false,
            motionTracking: false,
            zoomControl: false,
            imageDateControl: true,
            visible: true,
            disableDefaultUI: true,
            controlSize: 0,
          };

          let pano = panoramaRef.current;
          if (!pano) {
            pano = new maps.StreetViewPanorama(streetViewRef.current!, options);
            panoramaRef.current = pano;
          } else {
            pano.setPano(panoId);
          }

          if (panoramaStatusListenerRef.current) {
            panoramaStatusListenerRef.current.remove();
            panoramaStatusListenerRef.current = null;
          }
          panoramaStatusListenerRef.current = pano.addListener('status_changed', () => {
            setIsStreetViewFound(pano.getStatus?.() === maps.StreetViewStatus.OK);
          });

          const ll = panoLatLng;
          setSvPosition({ lat: ll.lat(), lng: ll.lng() });

          // Face the dwelling if we know it
          if (location && maps?.geometry?.spherical) {
            const panoPos = panoLatLng;
            const target = new maps.LatLng(location.lat, location.lng);
            let heading = maps.geometry.spherical.computeHeading(panoPos, target);
            heading = ((heading % 360) + 360) % 360; // normalize to [0, 360)

            const pov = pano.getPov();
            pano.setPov({
              heading,
              pitch: pov?.pitch ?? 0,
            });
            pano.setZoom(0);
            setSvHeading(heading);
          } else {
            const pov = pano.getPov();
            setSvHeading(pov?.heading ?? 0);
          }

          clearSvListeners();
          posListenerRef.current = pano.addListener('position_changed', () => {
            const p = pano.getPosition();
            if (p) setSvPosition({ lat: p.lat(), lng: p.lng() });
          });
          povListenerRef.current = pano.addListener('pov_changed', () => {
            const p = pano.getPov();
            if (p && typeof p.heading === 'number') setSvHeading(p.heading);
          });
        },
      );
    },
    [location, clearSvListeners],
  );

  // Initialisation of street view + cleanup
  useEffect(() => {
    if (!googleApiKey || !location) return;

    let isMounted = true;
    const loader = new Loader({ apiKey: googleApiKey, version: 'weekly' });

    const init = async () => {
      await loader.importLibrary('streetView');
      await loader.importLibrary('geometry');
      if (!isMounted) return;
      loadPanoramaForLatLng(location);
    };
    void init();

    return () => {
      isMounted = false;
      clearSvListeners();
      if (panoramaRef.current) {
        panoramaRef.current.setVisible(false);
        if (typeof panoramaRef.current?.unbindAll === 'function') {
          panoramaRef.current.unbindAll();
        }
        panoramaRef.current = null;
      }
      if (panoramaStatusListenerRef.current) {
        panoramaStatusListenerRef.current.remove();
        panoramaStatusListenerRef.current = null;
      }
    };
  }, [clearSvListeners, googleApiKey, location, loadPanoramaForLatLng]);

  // Using a CSS grid as it's the only way to change the layout without having to re-mount streetview + map
  const gridTemplateAreas = useMemo(
    () =>
      activeArea === 'magicplan'
        ? `
        "addressDetails  magicplan"
        "map             magicplan"
        "propertyDetails magicplan"
      `
        : `
        "addressDetails  map"
        "magicplan       map"
        "propertyDetails map"
      `,
    [activeArea],
  );

  const getMapStyle = () => {
    const sharedStyles = {
      overflow: 'hidden',
      zIndex: 1,
      flex: 1,
    };
    if (isMapActive) {
      return isMapExpanded
        ? {
            ...sharedStyles,
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '50%',
            boxShadow: '0 8px 36px #00000040',
            transition: isMapTransitionEnabled ? 'left 0.3s, bottom 0.3s, width 0.3s, height 0.3s' : 'none',
          }
        : {
            ...sharedStyles,
            position: 'absolute',
            bottom: 12,
            left: 12,
            width: '200px',
            height: '150px',
            boxShadow: '0 8px 36px #00000040',
            borderRadius: 1,
            transition: isMapTransitionEnabled ? 'left 0.3s, bottom 0.3s, width 0.3s, height 0.3s' : 'none',
          };
    } else {
      return {
        ...sharedStyles,
        position: 'relative',
        bottom: 'unset',
        left: 'unset',
        width: '100%',
        height: '100%',
        boxShadow: 0,
        borderRadius: 0,
      };
    }
  };

  const isMapActive = activeArea === 'map';
  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateAreas,
        gridTemplateColumns: '1fr 2fr',
        gridAutoRows: 'auto',
        columnGap: 2,
        width: '100%',
      }}
    >
      <Stack
        alignItems="center"
        justifyContent="center"
        sx={{
          width: '100%',
          backgroundColor: beige[100],
          p: 2,
          borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
          gridArea: 'addressDetails',
        }}
      >
        <Stack flexDirection="row" gap={2} sx={{ backgroundColor: surface[100], p: 1, borderRadius: 1, width: '100%' }}>
          <Stack gap={1} width="100%">
            <Typography variant="body1Emphasis">
              <FormattedMessage id="common.label.address" defaultMessage="Address" />
            </Typography>
            <Typography variant="body1">{address}</Typography>
          </Stack>
        </Stack>
      </Stack>

      {location && (
        <Stack
          alignItems="center"
          justifyContent="center"
          sx={{
            p: isMapActive ? 0 : 2,
            backgroundColor: isMapActive ? 'transparent' : beige[100],
            gridArea: 'map',
            position: 'relative',
          }}
        >
          <Grid
            container
            sx={{ height: '100%', cursor: 'pointer', borderRadius: 2, overflow: 'hidden', width: '100%' }}
          >
            <Stack
              direction={isMapActive ? 'column' : 'row'}
              sx={{ position: 'relative', minHeight: '200px', width: '100%' }}
            >
              <Box sx={{ flex: 1 }}>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    position: 'relative',
                    '.gm-iv-address': { display: 'none' },
                    '.gm-control-active': { display: 'none' },
                    flex: 1,
                    display: isStreetViewFound === false ? 'none' : 'block',
                  }}
                  ref={streetViewRef}
                />
                {isStreetViewFound === false && (
                  <Stack direction="column" alignItems="center" justifyContent="center" sx={{ p: 2, height: '100%' }}>
                    <Typography variant="body2Emphasis" sx={{ textAlign: 'center' }}>
                      <FormattedMessage id="heatDesign.propertyDetails.noStreetView" />
                    </Typography>
                  </Stack>
                )}
              </Box>
              <Box sx={getMapStyle()}>
                <Box
                  onClick={() => {
                    setActiveArea('map');
                    setTimeout(() => {
                      setIsMapTransitionEnabled(true);
                    });
                  }}
                  sx={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    width: '30px',
                    padding: 1,
                    height: '30px',
                    backgroundColor: grey[100],
                    transition: 'backgroundColor 0.2s',
                    zIndex: 401,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 1,
                    cursor: 'pointer',
                    boxShadow: '0 4px 8px #00000040',
                    display: isMapActive ? 'none' : 'flex',
                    '&:hover': { backgroundColor: grey[150] },
                  }}
                >
                  <OpenInFullIcon />
                </Box>
                <Box
                  sx={{
                    position: 'absolute',
                    left: '10px',
                    bottom: '10px',
                    width: '30px',
                    padding: 1,
                    height: '30px',
                    backgroundColor: grey[100],
                    transition: 'backgroundColor 0.2s',
                    zIndex: 401,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 1,
                    cursor: 'pointer',
                    display: isMapActive ? 'flex' : 'none',
                    boxShadow: '0 4px 8px #00000040',

                    '&:hover': { backgroundColor: grey[150] },
                  }}
                  onClick={() => {
                    setIsMapExpanded((prev) => !prev);
                  }}
                >
                  <OpenInFullIcon height="14px" width="14px" />
                </Box>
                <PropertyDetailsMap
                  isActive={isMapActive}
                  location={location}
                  streetViewPosition={svPosition ?? undefined}
                  streetViewHeading={svHeading ?? undefined}
                  onStreetviewMove={loadPanoramaForLatLng}
                />
              </Box>
              {isMapActive && (
                <Box
                  sx={{
                    width: '100%',
                    height: isMapExpanded ? '50%' : '0',
                    transition: 'height 0.2s',
                  }}
                />
              )}
            </Stack>
          </Grid>
        </Stack>
      )}

      <Stack
        justifyContent="center"
        alignItems="center"
        sx={{
          gridArea: 'propertyDetails',
          p: 2,
          borderRadius: `0 0 ${theme.spacing(2)} ${theme.spacing(2)}`,
          backgroundColor: beige[100],
        }}
      >
        <Stack gap={2} sx={{ width: '100%' }}>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', columnGap: 1 }}>
            <LabelValueDisplay
              sx={{ width: 'auto' }}
              label={<FormattedMessage id="heatDesign.propertyDetails.bedrooms" />}
              value={<Typography variant="body2Emphasis">{numBedrooms}</Typography>}
            />
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', columnGap: 1 }}>
              <LabelValueDisplay
                sx={{ width: 'auto' }}
                label={<FormattedMessage id="heatDesign.propertyDetails.bathrooms" />}
                value={<Typography variant="body2Emphasis">{numBathrooms}</Typography>}
              />
              <LabelValueDisplay
                data-testid="number-of-floors"
                sx={{ width: 'auto' }}
                label={<FormattedMessage id="heatDesign.propertyDetails.floors" />}
                value={<Typography variant="body2Emphasis">{numFloors}</Typography>}
              />
            </Box>
          </Box>
          <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', columnGap: 1 }}>
            <NumericTextField
              name="occupants"
              label={intl.formatMessage({ id: 'heatDesign.propertyDetails.occupants' })}
              value={numberOfResidents}
              onChange={actions.setNumberOfResidents}
              size="small"
              fullWidth
              error={!propertyIsValid('dwelling', 'numberOfResidents', numberOfResidents)}
            />

            <ConstructionYearForm />
          </Box>
        </Stack>
      </Stack>

      <Box
        sx={{
          gridArea: 'magicplan',
          p: isMapActive ? 2 : 0,
          backgroundColor: isMapActive ? beige[100] : 'transparent',
        }}
      >
        <Box sx={{ minHeight: '200px', position: 'relative', height: '100%' }} className="magicplan-wrapper">
          <Box
            onClick={() => {
              setIsMapTransitionEnabled(false);
              setActiveArea('magicplan');
              setIsMapExpanded(false);
            }}
            sx={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              width: '30px',
              padding: 1,
              height: '30px',
              backgroundColor: grey[100],
              transition: 'backgroundColor 0.2s',
              zIndex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 1,
              cursor: 'pointer',
              boxShadow: '0 4px 8px #00000040',
              display: isMapActive ? 'flex' : 'none',
              '&:hover': { backgroundColor: grey[150] },
            }}
          >
            <OpenInFullIcon height="14px" width="14px" />
          </Box>
          <iframe
            src={embed3dUrl}
            width="100%"
            height="100%"
            title="magicplan"
            style={{ borderRadius: theme.spacing(1), border: 'none' }}
          />
        </Box>
      </Box>
    </Box>
  );
}

export default PropertyDetails;
