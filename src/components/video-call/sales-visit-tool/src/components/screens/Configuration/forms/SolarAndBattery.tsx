import { useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { Form } from '../../../common/Form';
import { BoltSquareOutlinedIcon } from '@ui/components/StandardIcons/BoltSquareOutlinedIcon';
import { useProductSuspenseQuery } from '../../../../hooks/products';
import { useSolutionSuspenseQuery } from '../../../../hooks/solution';
import { configurationTranslations } from '../translations/translations';
import { type ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { useTranslation } from '../../../../hooks/translation';
import { Box, Typography } from '@mui/material';

export const SolarAndBattery = ({ onMouseOver }: { onMouseOver: (name: string) => void }) => {
  const [solution] = useSolutionSuspenseQuery();
  const [{ solarPanels, solarAndBatteries }] = useProductSuspenseQuery({
    priceListId: solution.priceListId,
    select: (data) => {
      const { solarPanels, solarAndBatteries } = data.products.system;
      return { solarPanels, solarAndBatteries };
    },
  });

  const defaultToggle = solution?.products.system.solarPanel
    ? 'solarPanel'
    : solution?.products.system.solarAndBatteryPackage
      ? 'solarAndBattery'
      : 'none';

  const [option, setOption] = useState<'none' | 'solarPanel' | 'solarAndBattery'>(defaultToggle);
  const { control, setValue } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);

  if (!solarPanels.length && !solarAndBatteries.length) {
    return null;
  }

  const options = [];

  if (solarPanels.length) {
    options.push({
      label: t.config.solar,
      value: 'solarPanel',
      selected: option === 'solarPanel',
    });
  }

  if (solarAndBatteries.length) {
    options.push({
      label: t.config.solarAndBattery,
      value: 'solarAndBattery',
      selected: option === 'solarAndBattery',
    });
  }
  options.push({
    label: t.common.no,
    value: 'none',
    selected: option === 'none',
  });

  return (
    <Box sx={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: 5 }}>
      <BoltSquareOutlinedIcon height={40} width={40} style={{ position: 'absolute', marginLeft: -78, marginTop: -4 }} />
      <Typography variant="headline2">{t.config.solarPanelsAndBattery}</Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Form.ButtonGroup
          label={t.config.addSolarPanels}
          onSelect={(x) => {
            if (x === 'none') {
              setValue('solarPanel', null, { shouldDirty: true });
              setValue('solarAndBatteryPackage', null, { shouldDirty: true });
            }
            setOption(x as typeof option);
          }}
          options={options}
          dataField="solarPanelsAndBattery"
          onMouseOver={onMouseOver}
        />
        {option === 'solarPanel' && (
          <Controller
            control={control}
            name="solarPanel"
            render={({ field: { onChange, value, name } }) => (
              <Form.Dropdown
                label={t.config.model}
                onChange={(e) => {
                  setValue('solarAndBatteryPackage', null);
                  onChange(e);
                }}
                options={solarPanels.map((x) => ({
                  label: x.name ?? '',
                  value: x.sku ?? null,
                  selected: value === x.sku,
                }))}
                dataField={name}
                onMouseOver={() => onMouseOver(name)}
              />
            )}
          />
        )}

        {option === 'solarAndBattery' && (
          <Controller
            control={control}
            name="solarAndBatteryPackage"
            render={({ field: { onChange, value, name } }) => (
              <Form.Dropdown
                label={t.config.model}
                onChange={(e) => {
                  setValue('solarPanel', null);
                  onChange(e);
                }}
                options={solarAndBatteries.map((x) => ({
                  label: x.name ?? '',
                  value: x.sku ?? null,
                  selected: value === x.sku,
                }))}
                dataField={name}
                onMouseOver={() => onMouseOver(name)}
              />
            )}
          />
        )}
      </Box>
    </Box>
  );
};
