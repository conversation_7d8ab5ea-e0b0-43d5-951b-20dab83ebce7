import { Controller, useFormContext } from 'react-hook-form';
import { Form } from '../../../common/Form';
import { HouseWave3VerticalOutlinedIcon } from '@ui/components/StandardIcons/HouseWave3VerticalOutlinedIcon';
import { useProductSuspenseQuery } from '../../../../hooks/products';
import { configurationTranslations } from '../translations/translations';
import { useTranslation } from '../../../../hooks/translation';
import { type ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { Box, Typography } from '@mui/material';

export const TechnicalSurveyAndSystemDesign = ({
  priceListId,
  onMouseOver,
}: {
  priceListId: string;
  onMouseOver: (name: string) => void;
}) => {
  const { control } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);
  const [{ technicalSurvey, systemDesign }] = useProductSuspenseQuery({
    priceListId,
    select: (data) => ({
      technicalSurvey: data.products.addonsAndExtras.technicalSurvey,
      systemDesign: data.products.addonsAndExtras.systemDesign,
    }),
  });

  if (!technicalSurvey && !systemDesign) {
    return null;
  }

  return (
    <Box sx={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: 5 }}>
      <HouseWave3VerticalOutlinedIcon
        height={40}
        width={40}
        style={{ position: 'absolute', marginLeft: -78, marginTop: -8 }}
      />
      <Typography variant="headline2">{t.config.technicalSurveyAndSystemDesign}</Typography>
      {technicalSurvey && (
        <Controller
          control={control}
          name="technicalSurvey"
          render={({ field: { onChange, value, name } }) => {
            return (
              <Form.ButtonGroup
                label={t.config.heatSurvey}
                onSelect={(e) => {
                  onChange(e);
                }}
                options={[
                  {
                    label: t.common.yes,
                    value: technicalSurvey.sku,
                    selected: value === technicalSurvey?.sku,
                  },
                  {
                    label: t.common.no,
                    value: null,
                    selected: value === null,
                  },
                ]}
                dataField={name}
                onMouseOver={onMouseOver}
              />
            );
          }}
        />
      )}
      {systemDesign && (
        <Controller
          control={control}
          name="systemDesign"
          render={({ field: { onChange, value, name } }) => {
            return (
              <Form.ButtonGroup
                label={t.config.mcsSystemDesign}
                onSelect={(e) => {
                  onChange(e);
                }}
                options={[
                  {
                    label: t.common.yes,
                    value: systemDesign.sku,
                    selected: value === systemDesign?.sku,
                  },
                  {
                    label: t.common.no,
                    value: null,
                    selected: value === null,
                  },
                ]}
                dataField={name}
                onMouseOver={onMouseOver}
              />
            );
          }}
        />
      )}
    </Box>
  );
};
