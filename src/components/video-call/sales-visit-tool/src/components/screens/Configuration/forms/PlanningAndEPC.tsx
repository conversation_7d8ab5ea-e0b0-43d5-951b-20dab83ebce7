import { Controller, useFormContext } from 'react-hook-form';
import { Form } from '../../../common/Form';
import { useProductSuspenseQuery } from '../../../../hooks/products';
import { type ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { configurationTranslations } from '../translations/translations';
import { useTranslation } from '../../../../hooks/translation';
import { Box, Typography } from '@mui/material';
import { DocumentOutlinedIcon } from '@ui/components/StandardIcons/DocumentOutlinedIcon';

export const PlanningAndEPC = ({
  priceListId,
  onMouseOver,
}: {
  priceListId: string;
  onMouseOver: (name: string) => void;
}) => {
  const [{ epcRating, planningPermission }] = useProductSuspenseQuery({
    priceListId,
    select: (data) => ({
      epcRating: data.products.addonsAndExtras.epcRating,
      planningPermission: data.products.addonsAndExtras.planningPermission,
    }),
  });

  const { control } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);

  if (!planningPermission && !epcRating) {
    return null;
  }

  return (
    <Box sx={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: 5 }}>
      <DocumentOutlinedIcon height={40} width={40} style={{ position: 'absolute', marginLeft: -78, marginTop: -4 }} />
      <Typography variant="headline2">Planning & EPC</Typography>
      {planningPermission && (
        <Controller
          control={control}
          name="planningPermission"
          render={({ field: { onChange, value, name } }) => {
            return (
              <Form.ButtonGroup
                label={t.config.planningPermission}
                onSelect={(e) => {
                  onChange(e);
                }}
                options={[
                  {
                    label: t.common.yes,
                    value: planningPermission.sku,
                    selected: value === planningPermission.sku,
                  },
                  {
                    label: t.common.no,
                    value: null,
                    selected: value === null,
                  },
                ]}
                dataField={name}
                onMouseOver={onMouseOver}
              />
            );
          }}
        />
      )}
      {epcRating && (
        <Controller
          control={control}
          name="epcRating"
          render={({ field: { onChange, value, name } }) => {
            return (
              <Form.ButtonGroup
                label={t.config.updateEpcRating}
                onSelect={(e) => {
                  onChange(e);
                }}
                options={[
                  {
                    label: t.common.yes,
                    value: epcRating.sku,
                    selected: value === epcRating.sku,
                  },
                  {
                    label: t.common.no,
                    value: null,
                    selected: value === null,
                  },
                ]}
                dataField={name}
                onMouseOver={onMouseOver}
              />
            );
          }}
        />
      )}
    </Box>
  );
};
