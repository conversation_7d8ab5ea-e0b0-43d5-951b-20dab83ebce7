import MuiButton, { type ButtonProps as MuiButtonProps } from '@mui/material/Button';
import Link from 'next/link';
import { WarningIcon } from 'components/heat-design/components/WarningIcon';

export interface ButtonProps extends MuiButtonProps {
  label?: string;
  target?: string;
}

// Ideally this should be defined in the above invalid CSS variant (see comment above)
export function getButtonStartIcon(buttonIsValid: boolean): React.JSX.Element | undefined {
  return buttonIsValid ? undefined : (
    <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
  );
}

export function getButtonVariant(selected: boolean, valid: boolean): MuiButtonProps['variant'] {
  if (selected) {
    return 'contained';
  }
  if (valid) {
    return 'outlined';
  }
  return 'invalid';
}

export function Button({
  children,
  variant = 'contained',
  size = 'medium',
  disableElevation = true,
  label,
  color,
  ...props
}: ButtonProps) {
  // Done to prevent a slight shift in width/height when using the 'contained' variant

  const containedBorderAdjustment =
    variant === 'contained'
      ? {
          border: '1px solid transparent',
        }
      : {};
  return (
    <MuiButton
      disableRipple
      variant={variant}
      aria-label={label}
      disableElevation={disableElevation}
      LinkComponent={Link}
      size={size}
      {...(color && { color })}
      {...props}
      sx={{
        ...containedBorderAdjustment,
        ...(props.sx ?? {}),
      }}
    >
      {children}
      {label}
    </MuiButton>
  );
}
