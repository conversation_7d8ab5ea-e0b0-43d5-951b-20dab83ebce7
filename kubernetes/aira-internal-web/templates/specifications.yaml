---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
      name: http
    - port: 8081
      targetPort: 3001
      protocol: TCP
      name: metrics
  selector:
    app: {{ .Release.Name }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  replicas: {{ .Values.replicas }}
  strategy:
    type: 'RollingUpdate'
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: {{ .Values.replicas }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
        release: {{ .Release.Name }}
        revision: "{{ .Release.Revision }}"
    spec:
      serviceAccountName: {{ .Release.Name }}
      dnsPolicy: Default
      containers:
        - name: {{ .Release.Name }}
          image: "************.dkr.ecr.eu-north-1.amazonaws.com/{{ .Release.Name }}:{{ .Values.image.tag }}"
          env:
            - name: AWS_REGION
              value: "eu-north-1"
            - name: ENVIRONMENT
              value: "{{ .Values.environment }}"
            - name: GOOGLE_API_KEY
              value: "{{ .Values.googleApiKey }}"
            - name: LOQATE_API_KEY
              value: "{{ .Values.loqateApiKey }}"
            - name: BACKEND_ENDPOINT
              value: "https://web-backend.{{ .Values.environment }}-internal.airahome.com"
            - name: BASELINE_ENDPOINT
              value: "https://baseline-calculator.{{ .Values.environment }}-internal.airahome.com"
            - name: OPEN_ROUTE_SERVICE_ENDPOINT
              value: "https://open-route-service.{{ .Values.environment }}-internal.airahome.com"
            - name: PUBLIC_WEB_URL
              value: "{{ .Values.publicWebUrl }}"
            - name: QUOTE_WEB_URL
              value: "{{ .Values.quoteWebUrl }}"
            - name: PAYMENTS_WEB_URL
              value: "{{ .Values.paymentsWebUrl }}"
            - name: GRPC_ENDPOINT
              value: "web-backend.{{ .Values.environment }}-internal.airahome.com:9443"
            - name: INSTALLATION_GROUNDWORK_GRPC_ENDPOINT
              value: "installation-groundwork.{{ .Values.environment }}-internal.airahome.com:443"
            - name: INSTALLATION_PROJECTS_GRPC_ENDPOINT
              value: "installation-projects.{{ .Values.environment }}-internal.airahome.com:443"
            - name: IDENTITY_SERVICE_GRPC_ENDPOINT
              value: "identity.{{ .Values.environment }}-internal.airahome.com:443"
            - name: MAGICPLAN_GRPC_ENDPOINT
              value: "magicplan.{{ .Values.environment }}-internal.airahome.com:443"
            - name: RESOURCE_SERVICE_GRPC_ENDPOINT
              value: "resource.{{ .Values.environment }}-internal.airahome.com:443"
            - name: SERVICE_VISITS_GRPC_ENDPOINT
              value: "service-visits.{{ .Values.environment }}-internal.airahome.com:443"
            - name: BILL_OF_MATERIALS_GRPC_ENDPOINT
              value: "bill-of-materials.{{ .Values.environment }}-internal.airahome.com:443"
            - name: MAN_HOURS_GRPC_ENDPOINT
              value: "baseline-calculator.{{ .Values.environment }}-internal.airahome.com:9443"
            - name: ON_SITE_DOSSIER_GRPC_ENDPOINT
              value: "on-site-dossier.{{ .Values.environment }}-internal.airahome.com:443"
            - name: HUBSPOT_INTEGRATION_GRPC_ENDPOINT
              value: "hubspot.{{ .Values.environment }}-internal.airahome.com:443"
            - name: SALES_SUPPORT_GRPC_ENDPOINT
              value: "sales-support.{{ .Values.environment }}-internal.airahome.com:443"
            - name: PARAMETER_STORE_AUTH_PRIVATE_KEY_NAME
              value: "/{{ .Values.environment }}/aira-internal-web/auth-key"
            - name: AUTH_ORIGIN
              value: "https://auth.{{ .Values.environment }}-internal.airahome.com"
            - name: AUTH_CLIENT_ID
              value: "aira-internal-web"
            - name: NEXT_PUBLIC_ENVIRONMENT
              value: "{{ .Values.environment }}"
            - name: NEXT_PUBLIC_SIGNALING_SERVER_URL
              value: "{{ .Values.signalingServerUrl }}"
            - name: NEXT_PUBLIC_LIVEKIT_URL
              value: "{{ .Values.livekitUrl }}"
            - name: LIVEKIT_API_KEY
              value: "{{ .Values.livekitApiKey }}"
            - name: LIVEKIT_API_SECRET
              value: "{{ .Values.livekitApiSecret }}"
            - name: NEXT_PUBLIC_CMS_CDN_URL
              value: "{{ .Values.cmsCdnUrl }}"
            - name: NEXT_PUBLIC_ENABLE_BATTERY_SAVINGS
              value: "{{ .Values.enableBatterySavings }}"
            - name: NEXT_PUBLIC_ENABLE_OFFLINE_MODAL
              value: "{{ .Values.enableOfflineModal }}"
            - name: NEXT_PUBLIC_ENABLE_ORIENTATION_MODAL
              value: "{{ .Values.enableOrientationModal }}"
            - name: NEXT_PUBLIC_ENABLE_OTHER_ELECTRICITY_TOGGLE
              value: "{{ .Values.enableOtherElectricityToggle }}"
            - name: NEXT_PUBLIC_ENABLE_GAS_STANDING_CHARGE_TOGGLE
              value: "{{ .Values.enableGasStandingChargeToggle }}"
            - name: BACKOFFICE_ASSISTANT_ENDPOINT
              value: "https://backoffice-assistant.{{ .Values.environment }}-internal.airahome.com:9443"

          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            requests:
              memory: "{{ .Values.resources.memory }}"
              cpu: "{{ .Values.resources.cpuMin }}"
            limits:
              memory: "{{ .Values.resources.memory }}"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::{{ .Values.account_number }}:role/application-{{ .Release.Name }}-{{ .Values.environment }}"
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Release.Name }}
  labels:
    namespace: team-esd
    release: prometheus-community
spec:
  selector:
    matchLabels:
      app: {{ .Release.Name }}
  endpoints:
    - port: metrics
      path: "/metrics"
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Release.Name }}-external
  labels:
    app: {{ .Release.Name }}
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/healthcheck-path: "/api/healthcheck"
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/group.order: '2' # Set to 2 to ensure these rule are processed after the websocket rule (set in the websocket repo) so that the /socket.io path gets matched first
    alb.ingress.kubernetes.io/auth-type: oidc
    alb.ingress.kubernetes.io/auth-idp-oidc: >
      {
        "issuer": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/v2.0",
        "authorizationEndpoint": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/oauth2/v2.0/authorize",
        "tokenEndpoint": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/oauth2/v2.0/token",
        "userInfoEndpoint": "https://graph.microsoft.com/oidc/userinfo",
        "secretName": "aerospace-azure-oidc"
      }
    alb.ingress.kubernetes.io/actions.redirect-to-main: >
      {
        "type": "redirect",
        "redirectConfig": {
          "protocol": "HTTPS",
          "port": "443",
          "host": "{{ .Values.ingress.external.host }}",
          "path": "/#{path}",
          "query": "#{query}",
          "statusCode":"HTTP_301"
        }
      }

spec:
  ingressClassName: {{ .Values.publicAlbClassName }}
  rules:
    - host: {{ .Values.ingress.external.host }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ $.Release.Name }}
                port:
                  name: http
{{- range .Values.ingress.external.redirects }}
    - host: "{{ . }}"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: redirect-to-main
                port:
                  name: use-annotation
{{- end }}
