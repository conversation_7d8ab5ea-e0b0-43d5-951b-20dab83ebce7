import '@testing-library/jest-dom/vitest';
import { beforeEach } from 'vitest';

// vitest.setup.ts
// This file patches the jsdom environment to mimic our FixJSDOMEnvironment settings from Jest.
// It sets global.structuredClone and mocks window.scrollTo, window.print, and window.URL.createObjectURL.
const originalError = console.error;

// Silence react-testing-library warnings about act
// this warning is not relevant most of the time, and pollutes the logs
console.error = (...args: unknown[]) => {
  if (typeof args[0] === 'string' && args[0].includes('was not wrapped in act')) {
    return;
  }
  originalError(...args); // keep every other warning
};

vi.mock('next/router', async () => {
  const actual = await vi.importActual<any>('next/router');
  const mock = await import('./__mocks__/next/router');
  return {
    ...actual,
    useRouter: mock.useRouter,
  };
});

vi.mock('zustand');
window.global ||= window;

if (global && typeof global.structuredClone === 'undefined') {
  global.structuredClone = structuredClone;
}

if (global && global.window) {
  global.window.scrollTo = () => {};
  global.window.print = () => {};
  global.window.URL.createObjectURL = () => 'mock-url';
}

afterEach(() => {
  // Clean up any leftover DOM elements
  document.body.innerHTML = '';

  // Reset any global state that might persist
  if (global.window) {
    global.window.localStorage.clear();
    global.window.sessionStorage.clear();
  }
});

beforeEach(() => {
  if (!window.ResizeObserver) {
    class ResizeObserver {
      observe() {}

      unobserve() {}

      disconnect() {}
    }

    window.ResizeObserver = ResizeObserver;
  }
});
